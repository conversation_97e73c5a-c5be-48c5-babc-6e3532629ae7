<!-- 二次封装的el-select,修改默认样式，其他属性保持不变 -->
<script setup lang="ts">
  import type { DatePickerInstance, DatePickerProps as ElDatePickerProps } from 'element-plus'
  import { ElDatePicker } from 'element-plus'
  import { ExtractPublicPropTypes, h, mergeProps, ref } from 'vue'
  import DataTableIcon from '@/components/common/dataTable/DataTableIcon.vue'
  import 'element-plus/es/components/date-picker/style/css'

  //  定义插槽类型，导出给父组件使用
  type DatePickerSlot = InstanceType<typeof ElDatePicker>['$slots']
  defineSlots<DatePickerSlot>()

  // 导出原有属性，供父组件传入
  type DatePickerProps = ExtractPublicPropTypes<ElDatePickerProps>
  const props = withDefaults(defineProps<DatePickerProps>(), {
    type: 'datetime',
    clearable: false,
    format: 'yyyy-MM-dd HH:mm:ss',
    prefixIcon: () =>
      h(DataTableIcon, {
        icon: 'bfdx-xingzhuang',
      }),
    popperClass: 'data-table-el-date-picker-popper',
  })

  // 暴露el-select ref给外部
  const rawRef = ref<DatePickerInstance | null>(null)
  defineExpose<DatePickerInstance>(
    new Proxy(
      {},
      {
        get: (_target, key) => rawRef.value?.[key as keyof DatePickerInstance],
        has: (_target, key) => key in (rawRef.value || {}),
      }
    ) as DatePickerInstance
  )
</script>

<template>
  <el-date-picker v-bind="mergeProps($attrs, props)" ref="rawRef">
    <template #prev-month>
      <slot v-if="$slots['prev-month']" name="prev-month" />
      <span v-else class="relative iconfont bfdx-xiala before:text-[25px] leading-none aspect-square inline-block rotate-90"></span>
    </template>
    <template #next-month>
      <slot v-if="$slots['next-month']" name="next-month" />
      <span v-else class="relative iconfont bfdx-xiala before:text-[25px] leading-none aspect-square inline-block -rotate-90"></span>
    </template>
    <template #prev-year>
      <slot v-if="$slots['prev-year']" name="prev-year" />
      <span v-else class="relative iconfont bfdx-xiala before:text-[25px] leading-none aspect-square inline-block rotate-90"></span>
    </template>
    <template #next-year>
      <slot v-if="$slots['next-year']" name="next-year" />
      <span v-else class="relative iconfont bfdx-xiala before:text-[25px] leading-none aspect-square inline-block -rotate-90"></span>
    </template>
    <template v-for="(_, slotName) in $slots" #[slotName]="slotProps">
      <template v-if="!['prev-month', 'next-month', 'prev-year', 'next-year'].includes(slotName + '')">
        <slot :name="slotName" v-bind="slotProps"></slot>
      </template>
    </template>
  </el-date-picker>
</template>

<style lang="scss">
  $DataTableElDatePickerBaseColor: #1398e9;
  .data-table-el-date-picker-popper {
    &.el-popper.el-picker__popper {
      background-image:
        linear-gradient(rgba($color: #003f6b, $alpha: 0.8), rgba($color: #003f6b, $alpha: 0.8)),
        linear-gradient(to bottom, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
      background-clip: padding-box, border-box;
      background-origin: border-box;
      border: 1px solid transparent;
      background-color: transparent;
      border-radius: 10px;
    }
    .el-popper__arrow {
      &::before {
        background: rgba($color: #003f6b, $alpha: 0.8) !important;
        border: 1px solid rgba(80, 213, 255, 0.63) !important;
        clip-path: polygon(0 0, 0% 100%, 100% 0);
      }
    }

    .el-picker-panel {
      width: 239.5px;
      height: 338px;
      background: transparent;
      border-radius: 10px;
      color: #fff;
      line-height: 1;

      .el-picker-panel__body {
        background: transparent;

        .el-date-picker__time-header {
          border-bottom: 0;
          padding: 10px 10px 0;
          display: flex;
          gap: 10px;
          .el-date-picker__editor-wrap {
            padding: 0;
          }
          .el-input__wrapper {
            background-color: rgba($color: #003f6b, $alpha: 0.8);
            color: $DataTableElDatePickerBaseColor;
            box-shadow: 0 0 0 1px #3095bd inset;
            border-radius: 2px;
            height: 27px;
            padding: 0;
            .el-input__inner {
              font-size: 13px;
              text-align: center;
              color: $DataTableElDatePickerBaseColor;
            }
          }
          .el-time-panel {
            width: 102.75px;
            background: rgba($color: #003f6b, $alpha: 1);
            margin: 0;
            border: 1px solid #3095bd;
            box-shadow: none;
            border-radius: 0 0 2px 2px;
            .el-time-panel__content {
              &::before {
                border: 0;
              }
            }
            .el-time-spinner__list:after,
            .el-time-spinner__list:before {
              height: 98px;
            }
            .el-time-spinner__item {
              color: $DataTableElDatePickerBaseColor;
              font-size: 13px;
              height: 28px;
              line-height: 28px;
              text-align: center;
              &.is-active:not(.is-disabled) {
                color: #fff;
              }
              &:hover:not(.is-disabled):not(.is-active) {
                background-color: #fff;
              }
            }
            .el-time-panel__footer {
              display: flex;
              justify-content: center;
              align-items: center;
              gap: 10px;
              border-top: 1px solid #3095bd;
              height: 28px;
              padding: 0;
              .el-time-panel__btn {
                color: $DataTableElDatePickerBaseColor;
                margin: 0;
                &.confirm {
                  color: #fff;
                }
              }
            }
          }
        }

        .el-date-picker__header {
          padding: 10px 10px 0;
          border: 0;
          .el-date-picker__header-label {
            color: #fff;
            font-size: 13px;
            line-height: 25px;
            padding: 0;
          }
          .el-date-picker__prev-btn,
          .el-date-picker__next-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            .el-picker-panel__icon-btn {
              margin-top: 0;
              height: 25px;
              color: $DataTableElDatePickerBaseColor;
            }
          }
        }

        .el-picker-panel__content {
          margin: 0 10px;
          width: 219.5px;
          .el-date-table-cell {
            padding: 0;
            height: 17px;
            .el-date-table-cell__text {
              font-size: 12px;
              line-height: 17px;
              height: 17px;
              text-align: center;
              color: $DataTableElDatePickerBaseColor;
            }
          }

          .el-date-table,
          .el-year-table,
          .el-month-table {
            tr:last-of-type {
              td {
                padding: 5px 0 10px;
                &:first-child {
                  border-bottom-left-radius: 7px;
                }
                &:last-child {
                  border-bottom-right-radius: 7px;
                }
              }
            }
          }

          .el-date-table tr:nth-of-type(2),
          .el-month-table tr:first-of-type,
          .el-year-table tr:first-of-type {
            td {
              padding: 10px 0 5px;
              &:first-child {
                border-top-left-radius: 7px;
              }
              &:last-child {
                border-top-right-radius: 7px;
              }
            }
          }

          .el-date-table {
            th {
              width: auto;
              padding: 10px 0;
              border: 0;
              color: $DataTableElDatePickerBaseColor;
              font-size: 12px;
              text-align: center;
            }
            .el-date-table__row {
              background-color: rgba($color: #000000, $alpha: 0.2);
            }
            td {
              padding: 5px 0;
              &.current:not(.disabled) .el-date-table-cell__text {
                background-color: #fff;
              }
              .el-date-table-cell {
                width: 17px;
                .el-date-table-cell__text {
                  width: 17px;
                }
              }
            }
          }

          .el-year-table,
          .el-month-table {
            width: auto;
            margin: 0;
            tr {
              background-color: rgba($color: #000000, $alpha: 0.2);
            }
            td {
              padding: 5px 0;
              &.current:not(.disabled) .el-date-table-cell__text {
                background-color: #fff;
              }
              .el-date-table-cell {
                width: 45px;
                .el-date-table-cell__text {
                  width: 45px;
                }
              }
            }
          }
        }
      }

      .el-picker-panel__footer {
        border: 0;
        padding: 10px 10px;
        background-color: transparent;
        color: $DataTableElDatePickerBaseColor;
        button.el-picker-panel__link-btn {
          width: 38px;
          height: 27px;
          color: $DataTableElDatePickerBaseColor;
          font-size: 13px;
          background-color: transparent;
          border-radius: 2px;
          &:last-child {
            margin-left: 5px;
            padding: 4.5px 10px;
            color: #fff;
          }
        }
      }
    }
  }
</style>
